import React from "react";
import { Pagination, Tooltip } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { JobRequestInterface } from "@src/redux/interfaces";
import { openDialog, updateJobRequestDetail } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { downloadFile } from "@src/helper/downloadFile";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import { encrypt, encryptString } from "@src/helper/encryption";
import Link from "next/link";
import { Download } from "@mui/icons-material";
import { secureFilename } from "@src/helper/common";
import { Button } from "react-bootstrap";

type WildcardEsignatureListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
};

export function WildcardEsignatureList({
  fetchData,
}: WildcardEsignatureListProps) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");
  const candidatesPermissions =
    useEmployeeSelectedPagePermissions("candidates");

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.jobRequest,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  // Open modal to edit jobRequest details
  const openSaveCandidate = (jobRequest: JobRequestInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.SAVE_CANDIDATE_REQUEST,
        options: {
          job: jobRequest,
          onSaveCandidate: (data: JobRequestInterface) =>
            openScheduleInterviewConfirmation(jobRequest, data),
        },
      }),
    );
  };

  const openScheduleInterviewConfirmation = (
    jobRequest: JobRequestInterface,
    data: JobRequestInterface,
  ) => {
    dispatch(
      updateJobRequestDetail({
        id: jobRequest.id,
        data: { ...jobRequest, ...data },
      }),
    );

    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          job: jobRequest,
          message: (
            <div className="mt-2 mb-2">
              Do you want to schedule an interview with this candidate for the
              position of {jobRequest.opportunity_title}?
            </div>
          ),
          onConfirm: () => openScheduleInterview(jobRequest),
        },
      }),
    );
  };

  const openScheduleInterview = (jobRequest: JobRequestInterface) => {
    if (jobRequest.candidate_id) {
      let encryptedJobId = encryptString(jobRequest.opportunity_id.toString());
      const jobParams = new URLSearchParams();
      jobParams.append("job_id", encryptedJobId);
      router.push(
        `${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(jobRequest.candidate_id.toString())}/interview?${jobParams.toString()})}`,
      );
    }
  };

  const hasRows = rows && rows.length > 0;

  const hasInterviewWritePermission = interviewsPermissions.includes("write");
  const hasCandidateWritePermission = candidatesPermissions.includes("write");

  return (
    <>
      <div
        className={`table-responsive job-request-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              <th className="mw-100px">Employee</th>
              <th className="mw-100px">Email</th>
              <th className="mw-100px">CreatedAt</th>
              <th className="mw-80px">Signature</th>
              <th className="mw-80px">Active</th>
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((employee: any, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>{employee?.name}</td>
                  <td>{employee?.email}</td>
                  <td>
                    {employee.created_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                      {employee?.signature}
                  </td>
                  <td>
                      <input></input>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={6} className="text-center">
                  No Records Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
