import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllJobRequestList, setLoader } from "@src/redux/actions";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import {
    KeyPairInterface,
    PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { WildcardEsignatureList } from "@src/components/WildCard/ESignature/WildcardEsignatureList";
import { Button } from "antd";
import Link from "next/link";


interface SearchParams {
    page: number;
    search?: string;
}

type ESignatureManagementPageProps = {
    subdomain: string;
    allowedRoles?: string[];
    pagePermissions?: PagePermissionInterface;
    requiredPermission?: string[];
    filters: SearchParams;
    pageDetail: KeyPairInterface;
};


export default function ESignatureManagementPage({
    subdomain,
    pagePermissions,
    filters,
    pageDetail,
}: ESignatureManagementPageProps) {


    const dispatch = useAppDispatch();
    const router = useRouter();
    const { currentPage, limit, rows } = useSelector(
        (state: RootState) => state.jobRequest,
    );

    const currentPagePermissions: string[] = useEmployeePagePermissions({
        pagePermissions,
    });

    const fetchData = useCallback(
        async (currentPage: number, limit: number) => {
            await dispatch(setLoader(true));
            dispatch(
                getAllJobRequestList({
                    page: currentPage,
                    limit: limit,
                }),
            );
            await dispatch(setLoader(false));
        },
        [dispatch],
    );

    useEffect(() => {
        if (subdomain) {
            fetchData(filters?.page ?? 1, limit ?? 10);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [subdomain]);

    useEffect(() => {
        let queryParams: SearchParams = { page: currentPage };
        // router.push(
        //     { pathname: APP_ROUTE.JOB_REQUEST_MANAGEMENT, query: queryParams as any },
        //     undefined,
        //     { shallow: true },
        // );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentPage]);

      const addNewButton = currentPagePermissions.includes("write") ? (
            <Link
            href={`${APP_ROUTE.ESIGNATURE_MANAGEMENT}/new`.subdomainLink(subdomain)}
            className="no-decoration ms-auto">
                <Button className="btn btn-theme ms-auto">
                    + Add Signature
                </Button>
            </Link>
        ) : null;


    return (
        <>
            <section className="">
                <PageHeader
                    pageTitle={pageDetail?.title ?? "E-Signature Management"}
                    pageDescription={
                        pageDetail?.description ?? "E-Signature Management Description"
                    }
                    buttonComponent={rows.length >= 0 && addNewButton}
                    >
                        
                    <div className="box-content">
                        <WildcardEsignatureList
                            fetchData={fetchData}
                            subdomain={subdomain}
                        />
                    </div>
                </PageHeader>
            </section>

        </>
    )
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
    req,
    res,
    query,
}) => {
    const {
        redirectUrl,
        validate,
        subdomain,
        allowedRoles,
        pagePermissions,
        pageDetail,
    } = await extractSubdomainAndDomain(req, { wildcard: true }, "job_requests");
    if (!validate) {
        return {
            redirect: {
                destination: redirectUrl,
                permanent: false,
            },
        };
    }

    let filters: SearchParams = {
        page: 1,
    };

    if (query) {
        const { search } = query;
        const page = query.page ? Number(query.page) : 1;
        // `search` should be a string or undefined
        const searchString = typeof search === "string" ? search : undefined;

        // Construct filters object with default values or undefined
        filters = {
            ...filters, // Spread the existing properties (if any)
            ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
            ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
        };
    }

    return {
        props: {
            subdomain: subdomain,
            allowedRoles: allowedRoles,
            pagePermissions: pagePermissions,
            requiredPermission: ["read"],
            filters: filters,
            pageDetail: pageDetail,
        },
    };
};

ESignatureManagementPage.layout = PrivateLayout;
