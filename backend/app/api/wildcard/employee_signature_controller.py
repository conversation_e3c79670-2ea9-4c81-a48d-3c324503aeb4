from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    Request,
)
from app.schema import PaginationResponse, SuccessResponse
from app.models import Employee, EmployeeSignature
from app.validations import StringValidate
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON><PERSON>elper
from app.constants import <PERSON><PERSON><PERSON>ame, PermissionName
from typing import Optional
from peewee import fn
import logging

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/employee-signatures",
    tags=["Wildcard Employee Signature API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)


def get_employee_signature(id: int):
    """
    Dependency to get an employee signature by ID.
    
    Args:
        id (int): The ID of the employee signature.
        
    Returns:
        EmployeeSignature: The employee signature object.
        
    Raises:
        RecordNotFoundException: If the employee signature is not found.
    """
    employee_signature = EmployeeSignature.get_or_none(EmployeeSignature.id == id)
    if not employee_signature:
        raise RecordNotFoundException("Employee signature not found")
    return employee_signature


@router.get(
    "/",
    summary="Get Employee Signatures",
    description="Retrieve a paginated list of employee signatures.",
    response_model=PaginationResponse,
)
def get_employee_signatures(
    request: Request,
    page: int = Query(1, gt=0),
    search: Optional[str] = Query(
        None, description="Search query based on employee name or email"
    ),
    limit: int = Query(10, gt=0, le=100),
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve a paginated list of employee signatures.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of signatures per page. Defaults to 10.
        search (Optional[str]): Search term to filter by employee name or email.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        PaginationResponse: Paginated list of employee signatures.
    """
    try:
        # Base query with employee join
        base_query = (
            EmployeeSignature.select(
                EmployeeSignature,
                Employee.first_name,
                Employee.last_name,
                Employee.email
            )
            .join(Employee)
            .where(Employee.business_id == employee.business_id)
        )

        search = search.strip().lower() if search else ""
        if search:
            search_filter = (
                (fn.lower(Employee.first_name).contains(search))
                | (fn.lower(Employee.last_name).contains(search))
                | (fn.lower(Employee.email).contains(search))
                | (
                    fn.lower(
                        fn.CONCAT(Employee.first_name, " ", Employee.last_name)
                    ).contains(search)
                )
            )
            base_query = base_query.where(search_filter)

        # Get total count
        total_count = base_query.count()

        # Apply pagination
        offset = (page - 1) * limit
        signatures = base_query.offset(offset).limit(limit)

        # Format response data
        signature_list = []
        for signature in signatures:
            signature_data = signature.info()
            signature_data.update({
                "employee_name": f"{signature.employee.first_name} {signature.employee.last_name}".strip(),
                "employee_email": signature.employee.email,
            })
            signature_list.append(signature_data)

        return PaginationResponse(
            message="Employee signatures retrieved successfully",
            data={
                "rows": signature_list,
                "count": total_count,
                "page": page,
                "limit": limit,
            },
        )
    except Exception as e:
        logging.error(f"Exception in get employee signatures: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving employee signatures",
        )

@router.post(
    "/",
    summary="Create Employee Signature",
    description="Create a new employee signature.",
    response_model=SuccessResponse,
)
async def create_employee_signature(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "employee_id": 1,
            "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
            "status": 1,
        },
    ),
):
    """
    Endpoint to create a new employee signature.

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing signature information.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_WRITE,
    )
    
    try:
        # Validate employee_id
        employee_id = body.get("employee_id")
        if not employee_id:
            raise ValueError("Employee ID is required")

        # Validate that the employee exists and belongs to the same business
        target_employee = Employee.get_or_none(
            Employee.id == employee_id,
            Employee.business_id == current_employee.business_id
        )
        if not target_employee:
            raise ValueError("Employee not found")

        # Check if signature already exists for this employee
        existing_signature = EmployeeSignature.get_or_none(
            EmployeeSignature.employee_id == employee_id
        )
        if existing_signature:
            raise ValueError("Signature already exists for this employee")

        # Validate signature
        signature = body.get("signature")
        if not signature:
            raise ValueError("Signature is required")

        # Validate status
        signature_status = body.get("status", 1)
        if signature_status not in [0, 1]:
            raise ValueError("Status must be 0 (inactive) or 1 (active)")

        # Create new employee signature
        new_signature = EmployeeSignature.create(
            employee_id=employee_id,
            signature=signature,
            status=signature_status,
        )

        # Return success response with signature details
        return SuccessResponse(
            message="Employee signature created successfully.",
            data=new_signature.info()
        )
    except Exception as e:
        logging.error(f"Exception in create employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the employee signature",
        )

@router.patch(
    "/{id}",
    summary="Update Employee Signature",
    description="Update an existing employee signature",
    response_model=SuccessResponse,
)
async def update_employee_signature(
    request: Request,
    signature: EmployeeSignature = Depends(get_employee_signature),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
            "status": 1,
        },
    ),
):
    """
    Update an existing employee signature.

    Args:
        signature (EmployeeSignature): The signature object to update.
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated signature information.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """
    try:
        # Verify that the signature belongs to an employee in the same business
        if signature.employee.business_id != current_employee.business_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Update signature if provided
        if "signature" in body:
            signature.signature = body["signature"]

        # Update status if provided
        if "status" in body:
            signature_status = body["status"]
            if signature_status not in [0, 1]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Status must be 0 (inactive) or 1 (active)"
                )
            signature.status = signature_status

        # Save the updated signature
        signature.save()

        return SuccessResponse(
            message="Employee signature updated successfully.",
            data=signature.info()
        )
    except Exception as e:
        logging.error(f"Exception in update employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the employee signature",
        )
