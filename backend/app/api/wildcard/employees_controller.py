from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    Request,
)
from app.schema import PaginationResponse, SuccessResponse
from app.models import (
    Employee,
    Candidate,
    Business,
    EmployeeRole,
    Node,
    BusinessEmployeeRolePermission,
    Permission,
    Opportunity,
    ScheduleInterview,
)
from app.validations import StringValidate, EmailValidate
from app.exceptions import RecordNotFoundException
from app.utils.password_utils import PasswordUtils
from app.helper import WildcardAuthHelper
from app.tasks import EmployeeTask
from typing import Optional
from peewee import fn
from app.constants import Constant, PermissionName, NodeName
import logging
from datetime import datetime


async def get_employee(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve an employee by their ID and business context.

    Args:
        id (int): The ID of the employee to retrieve.
        business (Business): The business context provided by dependency injection.

    Returns:
        Employee: The employee object if found.

    Raises:
        RecordNotFoundException: If the employee does not exist.
    """
    employee = Employee.get_or_none(id=id, business_id=business.id)
    if not employee or employee.employee_role.name == Constant.SUPER_ADMIN:
        raise RecordNotFoundException(message="Employee does not exist")
    if employee.id == current_employee.id:
        raise RecordNotFoundException(
            message="You are not authorized to perform this action"
        )
    return employee


router = APIRouter(
    prefix="/employees",
    tags=["Wilcard Employees API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)

# ------------------------------ router functions ------------------------------


# list of employees
@router.get(
    "",
    summary="List of Employees",
    description="Retrieve a paginated list of employees.",
    response_model=PaginationResponse,
)
def get_employees(
    request: Request,
    page: int = Query(1, gt=0),
    search: Optional[str] = Query(
        None, description="Search query based on first name, last name, or email"
    ),
    limit: int = Query(10, gt=0, le=100),
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_ONLY,
    )
    """
    Retrieve a paginated list of employees.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of employees per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of employees.
    """
    try:
        # Fetch total count
        employee_role = EmployeeRole.get_or_none(name=Constant.SUPER_ADMIN)
        base_query = Employee.where(
            {
                "business_id": employee.business_id,
                "employee_role_id": {"ne": employee_role.id},
            }
        )

        search = search.strip().lower() if search else ""
        if search:
            search_filter = (
                (fn.lower(Employee.first_name).contains(search))
                | (fn.lower(Employee.last_name).contains(search))
                | (fn.lower(Employee.email).contains(search))
                | (
                    fn.lower(
                        fn.CONCAT(Employee.first_name, " ", Employee.last_name)
                    ).contains(search)
                )
                | (
                    fn.lower(
                        fn.CONCAT(Employee.first_name, Employee.last_name)
                    ).contains(search)
                )
            )
            base_query = base_query.where(search_filter)

        base_query = base_query.order_by(Employee.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare employee list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in employee list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


# create a employee
@router.post(
    "",
    summary="Create New Employee",
    description="Create a new employee with the provided information.",
    response_model=SuccessResponse,
)
async def create_employees(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "first_name": "first_name of the employee",
            "last_name": "last_name of the employee",
            "email": "email of the employee",
            "contact_number": "contact number of the employee",
            "employee_role_id": "role_id of the employee",
        },
    ),
):
    """
    Endpoint to register a new employee.

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing employee information.

    Returns:
        SuccessResponse: Success message along with employee details upon successful registration.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        # Validate employee role
        employee_role = (
            EmployeeRole.select()
            .where(
                (EmployeeRole.name != Constant.SUPER_ADMIN)
                & (EmployeeRole.id == body.get("employee_role_id"))
                & (
                    (EmployeeRole.business_id == business.id)
                    | (EmployeeRole.business_id.is_null())
                )
            )
            .first()
        )

        if employee_role is None:
            raise ValueError("Employee Role must exist")

        # Validate email and ensure it is unique
        email = EmailValidate(body.get("email"), field="Email")
        employee_exist = (
            Employee.with_deleted()
            .where(
                Employee.email == email,
                Employee.business_id == current_employee.business_id,
            )
            .first()
        )
        if employee_exist:
            if employee_exist.employee_role.name == "Super Admin":
                raise ValueError(
                    "This email address is reserved for the SuperAdmin. Please use a different email address."
                )
            elif employee_exist.is_deleted:
                raise ValueError(
                    "The account associated with this email address has been deactivated. Please use an alternative email address to proceed."
                )
            else:
                raise ValueError("Employee already exists with this email")

        # Validate other fields
        first_name = StringValidate(
            body.get("first_name"),
            field="First Name",
            required=True,
            max_length=100,
            strip=True,
        )
        last_name = StringValidate(
            body.get("last_name"),
            field="Last Name",
            required=False,
            max_length=100,
            strip=True,
        )
        contact_number = StringValidate(
            body.get("contact_number"),
            field="Contact Number",
            required=False,
            max_length=100,
            strip=True,
        )
        password = PasswordUtils.generate_password()
        encrypted_password = PasswordUtils.hash_password(password)

        # Create new employee
        new_employee = Employee.create(
            email=email,
            password=encrypted_password,
            first_name=first_name,
            last_name=last_name,
            contact_number=contact_number,
            status=True,
            employee_role_id=employee_role.id,
            business_id=current_employee.business_id,
            created_by_id=current_employee.id,
        )

        # send a email to user with password credential
        EmployeeTask.new_employee_email.delay(new_employee.id, password)

        # Return success response with employee details
        return SuccessResponse(
            message="Employee Created Successfully.", data=new_employee.info()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logging.error(f"Exception in create a new employee: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating a employee",
        )


# get employee detail
@router.get(
    "/{id}",
    summary="Get Employee Info",
    description="Get employee information by ID",
    response_model=SuccessResponse,
)
async def get_employee_info(
    request: Request, employee: Employee = Depends(get_employee)
):
    """
    Get the information of an employee.

    Args:
        employee (Employee): The employee object provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and employee information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        return SuccessResponse(
            message="Employee details fetched successfully", data=employee.info()
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching employee details",
        )


# updaye employee detail
@router.put(
    "/{id}",
    summary="Update Employee",
    description="Update an existing employee with the provided information.",
    response_model=SuccessResponse,
)
async def update_employee(
    request: Request,
    employee: Employee = Depends(get_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "first_name": "First name of the employee",
            "last_name": "Last name of the employee",
            "email": "Email of the employee (optional)",
            "contact_number": "contact number of the employee",
            "employee_role_id": "Role ID of the employee",
        },
    ),
):
    """
    Endpoint to update an existing employee.

    Args:
        employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated employee information.

    Returns:
        SuccessResponse: Success message along with updated employee details upon successful update.

    Raises:
        HTTPException: If validation fails or other errors occur.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        # Validate employee role
        if "employee_role_id" in body:
            employee_role = (
                EmployeeRole.select()
                .where(
                    (EmployeeRole.name != Constant.SUPER_ADMIN)
                    & (EmployeeRole.id == body.get("employee_role_id"))
                    & (
                        (EmployeeRole.business_id == business.id)
                        | (EmployeeRole.business_id.is_null())
                    )
                )
                .first()
            )
            if employee_role is None:
                raise ValueError("Employee role must exist")
            employee.employee_role_id = employee_role.id

        # Validate and update other fields
        if "first_name" in body:
            employee.first_name = StringValidate(
                body.get("first_name"),
                field="First Name",
                required=True,
                max_length=100,
                strip=True,
            )
        if "last_name" in body:
            employee.last_name = StringValidate(
                body.get("last_name"),
                field="Last Name",
                required=False,
                max_length=100,
                strip=True,
            )
        if "contact_number" in body:
            employee.contact_number = StringValidate(
                body.get("contact_number"),
                field="Contact Number",
                required=False,
                max_length=100,
                strip=True,
            )
        # Save the updated employee
        employee.save()

        # Return success response
        return SuccessResponse(
            message="Employee updated successfully.", data=employee.info()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logging.error(f"Exception in updating employee: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the employee",
        )


# update emploee status
@router.put(
    "/{id}/status",
    response_model=SuccessResponse,
    summary="Update Employee Status",
    description="Update the status of an employee.",
)
def update_employee_status(
    request: Request,
    id: int,
    employee: Employee = Depends(get_employee),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of an employee.

    Args:
        id (int): The ID of the employee.
        employee (Employee): The employee instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated employee information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        employee_status = body.get("status")
        employee.status = employee_status
        employee.save()
        return SuccessResponse(
            message="Employee status updated successfully.", data=employee.info()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logging.error(f"Exception in updating employee status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating employee status",
        )


@router.delete(
    "/{id}",
    summary="Delete Employee",
    description="Delete the employee by marking them as deleted in the database.",
    response_model=SuccessResponse,
)
async def delete_employee(
    request: Request,
    employee: Employee = Depends(get_employee),
):
    """
    Soft delete an employee by setting the 'is_deleted' flag to True.
    This endpoint does not remove the employee record from the database, but it makes the employee
    invisible in the application by marking them as deleted. The deletion is reversible by updating
    the 'is_deleted' flag if needed.

    Args:
        employee (Employee): The employee object retrieved from the database based on the ID provided in the path parameter.

    Returns:
        SuccessResponse: A response indicating that the employee was successfully marked as deleted,
                         including the updated employee information.

    Raises:
        HTTPException: If the employee's status cannot be updated, either due to a validation error (422)
                       or an unexpected server error (500).
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_EMPLOYEES,
        perm_names=PermissionName.READ_DELETE,
    )
    try:
        employee.delete_instance()
        return SuccessResponse(message="Employee deleted successfully.")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logging.error(f"Exception in updating employee status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating employee status",
        )


@router.get(
    "/options/all",
    summary="Get Employees as Options",
    description="Retrieve an options list of employees.",
    response_model=SuccessResponse,
)
def get_employees_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    type: Optional[str] = Query(
        None, description="Have permission of interviewer and HR etc"
    ),
    search: Optional[str] = Query(None, description="Search term to filter employees"),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve an options list of employees.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of employees per page. Defaults to 10.
        search (Optional[str]): Search term to filter employees.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        employee_role = EmployeeRole.get_or_none(
            EmployeeRole.name == Constant.SUPER_ADMIN
        )
        base_query = Employee.where(
            {
                "business_id": business.id,
                "status": 1,
                "employee_role_id": {"ne": employee_role.id},
            }
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.LOWER(
                    fn.CONCAT(Employee.first_name, " ", Employee.last_name)
                ).contains(search)
                | fn.LOWER(Employee.email).contains(search)
            )

        if type is not None:
            read_permission = Permission.get(name="read")
            interview_node = Node.get(
                unique_key="employee_interviews", type="EmployeeNode"
            )
            candidate_node = Node.get(
                unique_key="employee_candidates", type="EmployeeNode"
            )

            base_permission_query = BusinessEmployeeRolePermission.select().where(
                BusinessEmployeeRolePermission.permission_id == read_permission.id,
                BusinessEmployeeRolePermission.business_id == business.id,
            )
            employee_role_ids = []
            if type == "Interviewer":
                employee_role_ids = BusinessEmployeeRolePermission.pluck_from_query(
                    base_permission_query.where(
                        BusinessEmployeeRolePermission.node_id == interview_node.id
                    ),
                    "employee_role_id",
                )

            elif type == "HR":
                employee_role_ids = BusinessEmployeeRolePermission.pluck_from_query(
                    base_permission_query.where(
                        (BusinessEmployeeRolePermission.node_id == candidate_node.id)
                        | (BusinessEmployeeRolePermission.node_id == interview_node.id)
                    ),
                    "employee_role_id",
                )

            base_query = base_query.where(
                Employee.employee_role_id.in_(employee_role_ids)
            )

        if showId is not None:
            base_query = base_query.orwhere(Employee.id == showId)
            base_query = base_query.order_by(
                fn.IF(Employee.id == showId, 0, 1), Employee.id
            )

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [
            {
                "label": f"{record.full_name()} - {record.email}",
                "value": record.id,
                "disabled": record.status == 0,
            }
            for record in records
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in employee list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/{id}/dependency",
    summary="Check Employee has dependency",
    description="Check Employee has dependency.",
    response_model=SuccessResponse,
)
async def delete_employee(
    employee: Employee = Depends(get_employee),
):
    """
    Soft delete an employee by setting the 'is_deleted' flag to True.
    This endpoint does not remove the employee record from the database, but it makes the employee
    invisible in the application by marking them as deleted. The deletion is reversible by updating
    the 'is_deleted' flag if needed.

    Args:
        employee (Employee): The employee object retrieved from the database based on the ID provided in the path parameter.

    Returns:
        SuccessResponse: A response indicating that the employee was successfully marked as deleted,
                         including the updated employee information.

    Raises:
        HTTPException: If the employee's status cannot be updated, either due to a validation error (422)
                       or an unexpected server error (500).
    """
    try:

        opportunities_count = (
            Opportunity.select()
            .where(Opportunity.contact_person_id == employee.id)
            .count()
        )
        running_interviews = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.interviewer_id == employee.id,
                ScheduleInterview.status.in_([1, 2, 6]),
            )
            .count()
        )

        has_dependency = opportunities_count > 0 or running_interviews > 0
        if opportunities_count > 0 and running_interviews > 0:
            message = f"You cannot delete the <strong>{employee.full_name()}</strong> because jobs (with Contact Person) and running interviews exist."
        elif opportunities_count > 0:
            message = f"You cannot delete the <strong>{employee.full_name()}</strong>  because jobs (with Contact Person) exist."
        elif running_interviews > 0:
            message = f"You cannot delete the <strong>{employee.full_name()}</strong>  because there are running interviews."
        else:
            # Proceed with the deletion logic
            message = f"<strong>{employee.full_name()}</strong> can be deleted."

        data = {
            "has_dependency": has_dependency,
            "message": message,
        }
        return SuccessResponse(message=message, data=data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logging.error(f"Exception in updating employee status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating employee status",
        )


@router.get(
    "/interviewer-schedule/{interviewer_id}",
    summary="Get Interviewer's Scheduled Interviews",
    description="Fetch all scheduled interview date-times for a specific interviewer, optionally filtered by timeframe.",
    response_model=SuccessResponse,
)
async def get_interviewer_schedule(
    interviewer_id: int,
    request: Request,
    start_date: Optional[datetime] = Query(
        None, description="Start datetime (ISO format)"
    ),
    end_date: Optional[datetime] = Query(None, description="End datetime (ISO format)"),
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Returns all interview date-times scheduled for a specific interviewer within the same business,
    optionally filtered by a start and end datetime.

    Args:
        interviewer_id (int): The ID of the interviewer.
        start_date (datetime, optional): Filter start datetime.
        end_date (datetime, optional): Filter end datetime.
        employee (Employee): The currently authenticated employee.

    Returns:
        SuccessResponse: List of scheduled interview date-times.
    """
    try:
        # Permission check
        WildcardAuthHelper.has_route_permission(
            request=request,
            node_key=NodeName.EMPLOYEE_INTERVIEWS,
            perm_names=PermissionName.READ_ONLY,
        )

        # Build query conditionally
        query = ScheduleInterview.select().where(
            (ScheduleInterview.interviewer_id == interviewer_id)
            & (ScheduleInterview.business_id == employee.business_id)
        )

        if start_date:
            query = query.where(ScheduleInterview.interview_at >= start_date)
        if end_date:
            query = query.where(ScheduleInterview.interview_at <= end_date)

        if not query.exists():
            return SuccessResponse(
                message="No interview schedule found for this interviewer.", data=[]
            )

        interview_slots = []

        for interview in query:
            try:

                interview_slots.append(
                    {
                        "interview_at": interview.interview_at,
                        "opportunity_id": interview.opportunity_id,
                        "opportunity_title": (
                            interview.opportunity.title
                            if interview.opportunity
                            else None
                        ),
                        "candidate_id": interview.candidate_id,
                        "candidate_name": (
                            interview.candidate.name if interview.candidate else None
                        ),
                        "interviewer_id": interview.interviewer_id,
                        "interviewer_name": (
                            interview.interviewer.first_name
                            if interview.interviewer
                            else None
                        ),
                        "interview_duration": interview.time_duration,
                    }
                )
            except Exception as inner_e:
                logging.warning(
                    f"Failed to fetch related data for interview ID {interview.id}: {inner_e}"
                )
                continue

        return SuccessResponse(
            message="Interview schedule fetched successfully.", data=interview_slots
        )

    except Exception as e:
        logging.exception("Failed to fetch interview schedule")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )
